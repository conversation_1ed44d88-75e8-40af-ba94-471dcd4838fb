import { useState, FormEvent } from 'react';
import { FaSearch } from 'react-icons/fa';
import { motion } from 'framer-motion';

interface SearchBarProps {
  onSearch: (query: string) => void;
  isLoading: boolean;
}

export default function SearchBar({ onSearch, isLoading }: SearchBarProps) {
  const [query, setQuery] = useState('');

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    if (query.trim() && !isLoading) {
      onSearch(query);
    }
  };

  return (
    <motion.div
      className="search-container"
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <form onSubmit={handleSubmit} className="relative">
        <FaSearch className="search-icon text-xl" />
        <input
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          placeholder="Search Wikipedia..."
          className="search-input"
          disabled={isLoading}
        />
        <motion.button
          type="submit"
          className={`absolute right-3 top-1/2 -translate-y-1/2 bg-primary hover:bg-primary/90 
                     text-white px-4 py-1.5 rounded-full transition-all duration-300 
                     ${isLoading ? 'opacity-70 cursor-not-allowed' : ''}`}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          disabled={isLoading}
        >
          {isLoading ? 'Searching...' : 'Search'}
        </motion.button>
      </form>
    </motion.div>
  );
}