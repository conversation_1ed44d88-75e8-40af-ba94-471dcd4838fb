@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
}

.dark {
  --background: #11111b;
  --foreground: #ededed;
}

@layer base {
  body {
    @apply bg-neutral-950 text-white font-sans antialiased;
  }
}

@layer components {
  .search-container {
    @apply w-full max-w-3xl mx-auto mt-20 px-4;
  }
  
  .search-input {
    @apply w-full bg-neutral-900 border-2 border-neutral-850 rounded-full py-3 px-6 pl-12 text-white 
           focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary
           transition-all duration-300;
  }
  
  .search-icon {
    @apply absolute left-4 top-1/2 -translate-y-1/2 text-gray-400;
  }
  
  .result-card {
    @apply bg-neutral-900 rounded-xl p-5 hover:bg-neutral-850 transition-all duration-300
           border border-neutral-850 hover:border-primary/30 cursor-pointer;
  }
  
  .summary-container {
    @apply bg-neutral-900 rounded-2xl p-6 md:p-8 border border-neutral-850 
           shadow-lg shadow-black/20 max-w-4xl mx-auto my-8;
  }
  
  .gradient-text {
    @apply bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent 
           animate-gradient bg-[length:200%_auto];
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-neutral-950;
}

::-webkit-scrollbar-thumb {
  @apply bg-neutral-850 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-primary/50;
}
