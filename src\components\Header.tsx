import { motion } from 'framer-motion';
import { FaWikipediaW } from 'react-icons/fa';

export default function Header() {
  return (
    <header className="w-full py-6 px-4">
      <div className="max-w-7xl mx-auto">
        <motion.div 
          className="flex items-center justify-center"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <FaWikipediaW className="text-3xl text-primary mr-2" />
          <h1 className="text-2xl font-display font-bold">
            <span className="gradient-text">Wiki</span>
            <span className="text-white">Search</span>
          </h1>
        </motion.div>
        <motion.p 
          className="text-center text-gray-400 mt-2 max-w-lg mx-auto"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          Search Wikipedia with a modern interface and get AI-powered summaries
        </motion.p>
      </div>
    </header>
  );
}