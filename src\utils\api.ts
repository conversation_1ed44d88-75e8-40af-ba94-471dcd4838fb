import axios from 'axios';

// Types
export interface WikiSearchResult {
  pageid: number;
  title: string;
  snippet: string;
}

export interface WikiContent {
  pageid: number;
  title: string;
  extract: string;
  thumbnail?: {
    source: string;
    width: number;
    height: number;
  };
}

// Search Wikipedia
export async function searchWikipedia(query: string): Promise<WikiSearchResult[]> {
  try {
    const response = await axios.get(`/api/wiki/search?query=${encodeURIComponent(query)}`);
    return response.data.query.search;
  } catch (error) {
    console.error('Error searching Wikipedia:', error);
    return [];
  }
}

// Get Wikipedia page content
export async function getWikipediaContent(pageId: number): Promise<WikiContent | null> {
  try {
    const response = await axios.get(`/api/wiki/content?pageId=${pageId}`);
    const page = response.data.query.pages[pageId];
    
    return {
      pageid: page.pageid,
      title: page.title,
      extract: page.extract,
      thumbnail: page.thumbnail
    };
  } catch (error) {
    console.error('Error fetching Wikipedia content:', error);
    return null;
  }
}

// Get AI summary of content
export async function getSummary(content: string, title: string, pageId: number): Promise<string> {
  try {
    const response = await axios.post('/api/summarize', {
      content,
      title,
      pageId
    });
    return response.data.summary;
  } catch (error) {
    console.error('Error getting summary:', error);
    return 'Failed to generate summary. Please try again later.';
  }
}