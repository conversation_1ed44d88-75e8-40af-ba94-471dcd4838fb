'use client';

import { useState } from 'react';
import Header from '@/components/Header';
import SearchBar from '@/components/SearchBar';
import SearchResults from '@/components/SearchResults';
import ContentSummary from '@/components/ContentSummary';
import { 
  searchWikipedia, 
  getWikipediaContent, 
  getSummary,
  WikiSearchResult,
  WikiContent
} from '@/utils/api';

export default function Home() {
  const [searchResults, setSearchResults] = useState<WikiSearchResult[]>([]);
  const [selectedContent, setSelectedContent] = useState<WikiContent | null>(null);
  const [summary, setSummary] = useState<string>('');
  const [isSearching, setIsSearching] = useState(false);
  const [isSummarizing, setIsSummarizing] = useState(false);

  const handleSearch = async (query: string) => {
    setIsSearching(true);
    setSelectedContent(null);
    setSummary('');
    
    try {
      const results = await searchWikipedia(query);
      setSearchResults(results);
    } catch (error) {
      console.error('Error searching:', error);
    } finally {
      setIsSearching(false);
    }
  };

  const handleSelectResult = async (result: WikiSearchResult) => {
    setIsSummarizing(true);
    
    try {
      const content = await getWikipediaContent(result.pageid);
      if (content) {
        setSelectedContent(content);
        
        // Get AI summary
        const summaryText = await getSummary(
          content.extract,
          content.title,
          content.pageid
        );
        setSummary(summaryText);
      }
    } catch (error) {
      console.error('Error fetching content:', error);
    } finally {
      setIsSummarizing(false);
    }
  };

  const handleBackToResults = () => {
    setSelectedContent(null);
    setSummary('');
  };

  return (
    <div className="min-h-screen bg-neutral-950 pb-20">
      <Header />
      
      {!selectedContent ? (
        <>
          <SearchBar onSearch={handleSearch} isLoading={isSearching} />
          <SearchResults 
            results={searchResults} 
            onSelectResult={handleSelectResult} 
          />
        </>
      ) : (
        <ContentSummary 
          content={selectedContent}
          summary={summary}
          onBack={handleBackToResults}
          isLoading={isSummarizing}
        />
      )}
    </div>
  );
}
