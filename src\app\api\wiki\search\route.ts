import { NextRequest, NextResponse } from "next/server";
import axios from "axios";

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const query = searchParams.get("query");

  if (!query) {
    return NextResponse.json(
      { error: "Query parameter is required" },
      { status: 400 }
    );
  }

  try {
    // Wikipedia API endpoint for searching
    const response = await axios.get(
      `https://en.wikipedia.org/w/api.php?action=query&list=search&srsearch=${encodeURIComponent(
        query
      )}&format=json&origin=*&srlimit=10`
    );

    return NextResponse.json(response.data);
  } catch (error) {
    console.error("Error fetching from Wikipedia API:", error);
    return NextResponse.json(
      { error: "Failed to fetch data from Wikipedia" },
      { status: 500 }
    );
  }
}