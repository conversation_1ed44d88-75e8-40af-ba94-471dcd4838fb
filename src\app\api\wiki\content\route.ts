import { NextRequest, NextResponse } from "next/server";
import axios from "axios";

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const pageId = searchParams.get("pageId");

  if (!pageId) {
    return NextResponse.json(
      { error: "Page ID parameter is required" },
      { status: 400 }
    );
  }

  try {
    // Wikipedia API endpoint for getting page content
    const response = await axios.get(
      `https://en.wikipedia.org/w/api.php?action=query&prop=extracts|pageimages&exintro=1&format=json&pageids=${pageId}&origin=*&pithumbsize=500`
    );

    return NextResponse.json(response.data);
  } catch (error) {
    console.error("Error fetching page content from Wikipedia API:", error);
    return NextResponse.json(
      { error: "Failed to fetch page content from Wikipedia" },
      { status: 500 }
    );
  }
}