import { motion } from 'framer-motion';
import { WikiSearchResult } from '@/utils/api';

interface SearchResultsProps {
  results: WikiSearchResult[];
  onSelectResult: (result: WikiSearchResult) => void;
}

export default function SearchResults({ results, onSelectResult }: SearchResultsProps) {
  if (results.length === 0) return null;

  return (
    <motion.div
      className="w-full max-w-3xl mx-auto mt-8 px-4 space-y-4"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5, delay: 0.2 }}
    >
      <h2 className="text-xl font-display font-medium text-white mb-4">
        Search Results
      </h2>
      
      <div className="space-y-4">
        {results.map((result, index) => (
          <motion.div
            key={result.pageid}
            className="result-card"
            onClick={() => onSelectResult(result)}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.05 }}
            whileHover={{ scale: 1.02 }}
          >
            <h3 className="text-lg font-display font-medium text-white mb-2">
              {result.title}
            </h3>
            <p 
              className="text-gray-300 text-sm"
              dangerouslySetInnerHTML={{ __html: result.snippet + '...' }}
            />
          </motion.div>
        ))}
      </div>
    </motion.div>
  );
}