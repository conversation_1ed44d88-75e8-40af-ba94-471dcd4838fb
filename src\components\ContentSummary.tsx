import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FaArrowLeft, FaExternalLinkAlt } from 'react-icons/fa';
import { WikiContent } from '@/utils/api';

interface ContentSummaryProps {
  content: WikiContent;
  summary: string;
  onBack: () => void;
  isLoading: boolean;
}

export default function ContentSummary({ 
  content, 
  summary, 
  onBack,
  isLoading 
}: ContentSummaryProps) {
  const [imageLoaded, setImageLoaded] = useState(false);

  useEffect(() => {
    setImageLoaded(false);
  }, [content.pageid]);

  return (
    <motion.div
      className="w-full max-w-4xl mx-auto mt-8 px-4"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <motion.button
        className="flex items-center gap-2 text-gray-300 hover:text-white mb-6 transition-colors"
        onClick={onBack}
        whileHover={{ x: -5 }}
        whileTap={{ scale: 0.95 }}
      >
        <FaArrowLeft /> Back to results
      </motion.button>

      <div className="summary-container">
        <div className="flex flex-col md:flex-row gap-6">
          {content.thumbnail && (
            <div className="w-full md:w-1/3 flex-shrink-0">
              <motion.div
                className="relative aspect-video md:aspect-square rounded-lg overflow-hidden bg-dark-100"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ 
                  opacity: imageLoaded ? 1 : 0.5, 
                  scale: imageLoaded ? 1 : 0.95 
                }}
                transition={{ duration: 0.5 }}
              >
                <img
                  src={content.thumbnail.source}
                  alt={content.title}
                  className="w-full h-full object-cover"
                  onLoad={() => setImageLoaded(true)}
                />
                {!imageLoaded && (
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-8 h-8 border-4 border-primary/30 border-t-primary rounded-full animate-spin"></div>
                  </div>
                )}
              </motion.div>
            </div>
          )}
          
          <div className={`flex-1 ${content.thumbnail ? 'md:w-2/3' : 'w-full'}`}>
            <h1 className="text-2xl md:text-3xl font-display font-bold mb-4 gradient-text">
              {content.title}
            </h1>
            
            <div className="mb-6">
              <h2 className="text-lg font-display font-medium text-white mb-2">
                Wikipedia Extract
              </h2>
              <div 
                className="text-gray-300 text-sm leading-relaxed"
                dangerouslySetInnerHTML={{ __html: content.extract }}
              />
            </div>

            <a 
              href={`https://en.wikipedia.org/?curid=${content.pageid}`}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center gap-2 text-primary hover:text-secondary transition-colors text-sm"
            >
              Read full article on Wikipedia <FaExternalLinkAlt />
            </a>
          </div>
        </div>

        <div className="mt-8 pt-6 border-t border-dark-100">
          <h2 className="text-xl font-display font-medium text-white mb-4 flex items-center gap-2">
            <span className="gradient-text">AI Summary</span>
            {isLoading && (
              <div className="w-4 h-4 border-2 border-primary/30 border-t-primary rounded-full animate-spin ml-2"></div>
            )}
          </h2>
          
          {isLoading ? (
            <div className="h-32 bg-dark-100/50 rounded-lg animate-pulse"></div>
          ) : (
            <motion.div
              className="text-gray-200 leading-relaxed"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              {summary.split('\n').map((paragraph, index) => (
                <p key={index} className="mb-4">
                  {paragraph}
                </p>
              ))}
            </motion.div>
          )}
        </div>
      </div>
    </motion.div>
  );
}