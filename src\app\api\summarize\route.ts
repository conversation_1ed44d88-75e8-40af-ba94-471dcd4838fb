import { NextRequest, NextResponse } from "next/server";
import axios from "axios";

// In-memory cache for summaries
const summaryCache: Record<string, { summary: string; timestamp: number }> = {};
const CACHE_EXPIRY = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

export async function POST(request: NextRequest) {
  try {
    const { content, title, pageId } = await request.json();
    
    if (!content || !title) {
      return NextResponse.json(
        { error: "Content and title are required" },
        { status: 400 }
      );
    }

    const cacheKey = `summary-${pageId}`;
    
    // Check if we have a cached summary that's not expired
    if (summaryCache[cacheKey] && 
        Date.now() - summaryCache[cacheKey].timestamp < CACHE_EXPIRY) {
      console.log("Returning cached summary for:", title);
      return NextResponse.json({ summary: summaryCache[cacheKey].summary });
    }

    // Get the Groq API key from environment variables
    const apiKey = process.env.GROQ_API_KEY;
    
    let summary: string;
    
    if (apiKey) {
      try {
        // Call Groq API for summarization
        summary = await getGroqSummary(content, title, apiKey);
      } catch (error) {
        console.error("Error calling Groq API:", error);
        // Fallback to simulated summary if Groq API fails
        summary = simulateAISummary(content, title);
      }
    } else {
      // If no API key is provided, use simulated summary
      console.log("No Groq API key found, using simulated summary");
      summary = simulateAISummary(content, title);
    }
    
    // Cache the summary
    summaryCache[cacheKey] = {
      summary,
      timestamp: Date.now()
    };

    return NextResponse.json({ summary });
  } catch (error) {
    console.error("Error generating summary:", error);
    return NextResponse.json(
      { error: "Failed to generate summary" },
      { status: 500 }
    );
  }
}

// Function to get summary from Groq API
async function getGroqSummary(content: string, title: string, apiKey: string): Promise<string> {
  // Remove HTML tags for processing
  const plainText = content.replace(/<[^>]*>/g, '');
  
  try {
    const response = await axios.post(
      'https://api.groq.com/openai/v1/chat/completions',
      {
        model: "llama3-8b-8192",
        messages: [
          {
            role: "system",
            content: "You are a helpful assistant that summarizes Wikipedia articles. Provide concise, informative summaries that capture the key points of the article. Include relevant facts and context. Format your response in clear paragraphs."
          },
          {
            role: "user",
            content: `Please summarize this Wikipedia article about "${title}":\n\n${plainText}`
          }
        ],
        temperature: 0.5,
        max_tokens: 500
      },
      {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    return response.data.choices[0].message.content;
  } catch (error) {
    console.error("Error calling Groq API:", error);
    throw error;
  }
}

// Function to simulate AI summarization as a fallback
function simulateAISummary(content: string, title: string): string {
  // Remove HTML tags for processing
  const plainText = content.replace(/<[^>]*>/g, '');
  
  // Get first few sentences (up to 500 chars)
  const firstPart = plainText.substring(0, 500);
  
  // Create a simulated summary
  return `${title} is ${plainText.length > 1000 ? 'a comprehensive' : 'an'} article about ${title.toLowerCase()}. 
  
  ${firstPart}... 
  
  This AI-generated summary highlights the key aspects of ${title}. The article contains approximately ${plainText.length} characters of information about this topic.`;
}